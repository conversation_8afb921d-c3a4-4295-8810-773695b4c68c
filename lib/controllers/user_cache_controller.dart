import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class UserCacheController extends GetxController {
  static UserCacheController get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Cache for user data with reactive updates
  final RxMap<String, Map<String, dynamic>> _userCache =
      <String, Map<String, dynamic>>{}.obs;

  // Cache for profile images specifically for faster access
  final RxMap<String, String> _profileImageCache = <String, String>{}.obs;

  // Getters
  Map<String, Map<String, dynamic>> get userCache => _userCache;
  Map<String, String> get profileImageCache => _profileImageCache;

  /// Get user data with caching and reactive updates
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    if (userId.isEmpty) return null;

    // Return cached data if available
    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final userData = doc.data()!;
        userData['id'] = doc.id;

        // Cache the result with reactive update
        _userCache[userId] = userData;

        // Cache profile image separately for quick access
        if (userData['profileImageUrl'] != null) {
          _profileImageCache[userId] = userData['profileImageUrl'];
        }

        debugPrint('UserCacheController: Cached user data for $userId');
        return userData;
      }
    } catch (e) {
      debugPrint(
        'UserCacheController: Error fetching user data for $userId: $e',
      );
    }

    return null;
  }

  /// Get profile image URL with caching
  Future<String?> getProfileImageUrl(String userId) async {
    // Return cached image URL if available
    if (_profileImageCache.containsKey(userId)) {
      return _profileImageCache[userId];
    }

    // Get from user data
    final userData = await getUserData(userId);
    final imageUrl = userData?['profileImageUrl'];

    if (imageUrl != null) {
      _profileImageCache[userId] = imageUrl;
    }

    return imageUrl;
  }

  /// Get display name with caching
  Future<String> getDisplayName(String userId) async {
    final userData = await getUserData(userId);
    return userData?['name'] ?? userData?['username'] ?? 'Unknown User';
  }

  /// Update user data in cache and notify all listeners
  void updateUserData(String userId, Map<String, dynamic> newData) {
    debugPrint(
      'UserCacheController: Updating user data for $userId with keys: ${newData.keys}',
    );

    // Create a new map to trigger reactive update
    final currentData = Map<String, dynamic>.from(_userCache[userId] ?? {});
    currentData.addAll(newData);

    // Update user cache with new reference
    _userCache[userId] = currentData;

    // Update profile image cache if changed
    if (newData.containsKey('profileImageUrl')) {
      _profileImageCache[userId] = newData['profileImageUrl'];
      // Force specific update for profile images
      update(['profile_image_$userId']);
    }

    // Force general controller update
    update();

    debugPrint(
      'UserCacheController: User data updated and UI notified for $userId',
    );
  }

  /// Update profile image in cache and notify all listeners
  void updateProfileImage(String userId, String newImageUrl) {
    debugPrint(
      'UserCacheController: Updating profile image for $userId to $newImageUrl',
    );

    // Update profile image cache first
    _profileImageCache[userId] = newImageUrl;

    // Update user cache if exists
    if (_userCache.containsKey(userId)) {
      final currentData = Map<String, dynamic>.from(_userCache[userId]!);
      currentData['profileImageUrl'] = newImageUrl;
      _userCache[userId] = currentData;
    }

    // Force specific updates for this user's profile images
    update(['profile_image_$userId']);

    // Also force a general update for any other listeners
    update();

    debugPrint(
      'UserCacheController: Profile image updated and UI notified for $userId',
    );
  }

  /// Clear cache for a specific user
  void clearUserCache(String userId) {
    _userCache.remove(userId);
    _profileImageCache.remove(userId);
    debugPrint('UserCacheController: Cleared cache for user $userId');
  }

  /// Clear all cached data
  void clearAllCache() {
    _userCache.clear();
    _profileImageCache.clear();
    debugPrint('UserCacheController: Cleared all cache');
  }

  /// Get multiple users data efficiently
  Future<Map<String, Map<String, dynamic>>> getMultipleUsersData(
    List<String> userIds,
  ) async {
    final result = <String, Map<String, dynamic>>{};
    final uncachedIds = <String>[];

    // Check cache first
    for (final userId in userIds) {
      if (_userCache.containsKey(userId)) {
        result[userId] = _userCache[userId]!;
      } else {
        uncachedIds.add(userId);
      }
    }

    // Fetch uncached users
    if (uncachedIds.isNotEmpty) {
      try {
        final docs =
            await _firestore
                .collection('users')
                .where(FieldPath.documentId, whereIn: uncachedIds)
                .get();

        for (final doc in docs.docs) {
          if (doc.exists) {
            final userData = doc.data();
            userData['id'] = doc.id;

            // Cache and add to result
            _userCache[doc.id] = userData;
            result[doc.id] = userData;

            // Cache profile image
            if (userData['profileImageUrl'] != null) {
              _profileImageCache[doc.id] = userData['profileImageUrl'];
            }
          }
        }
      } catch (e) {
        debugPrint(
          'UserCacheController: Error fetching multiple users data: $e',
        );
      }
    }

    return result;
  }

  /// Listen to real-time updates for a user
  void listenToUserUpdates(String userId) {
    _firestore.collection('users').doc(userId).snapshots().listen((doc) {
      if (doc.exists) {
        final userData = doc.data()!;
        userData['id'] = doc.id;

        updateUserData(userId, userData);
        debugPrint('UserCacheController: Real-time update for user $userId');
      }
    });
  }

  /// Get reactive stream for a user's profile image
  Stream<String?> getProfileImageStream(String userId) {
    return _profileImageCache.stream.map((cache) => cache[userId]);
  }

  /// Get reactive stream for a user's data
  Stream<Map<String, dynamic>?> getUserDataStream(String userId) {
    return _userCache.stream.map((cache) => cache[userId]);
  }
}
