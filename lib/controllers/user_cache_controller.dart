import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class UserCacheController extends GetxController {
  static UserCacheController get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Cache for user data with reactive updates
  final RxMap<String, Map<String, dynamic>> _userCache =
      <String, Map<String, dynamic>>{}.obs;

  // Cache for profile images specifically for faster access - using individual Rx variables
  final Map<String, RxString> _profileImageCache = {};

  // Reactive trigger for profile image updates
  final RxInt _profileImageUpdateTrigger = 0.obs;

  // Getters
  RxMap<String, Map<String, dynamic>> get userCache => _userCache;
  RxInt get profileImageUpdateTrigger => _profileImageUpdateTrigger;

  // Get reactive profile image URL for a specific user
  RxString getProfileImageRx(String userId) {
    if (!_profileImageCache.containsKey(userId)) {
      _profileImageCache[userId] = ''.obs;
      // Load initial data if not cached
      getUserData(userId);
    }
    return _profileImageCache[userId]!;
  }

  // Get profile image URL (non-reactive)
  String? getProfileImageUrlSync(String userId) {
    return _profileImageCache[userId]?.value;
  }

  /// Get user data with caching and reactive updates
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    if (userId.isEmpty) return null;

    // Return cached data if available
    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final userData = doc.data()!;
        userData['id'] = doc.id;

        // Cache the result with reactive update
        _userCache[userId] = userData;

        // Cache profile image separately for quick access
        if (userData['profileImageUrl'] != null) {
          if (!_profileImageCache.containsKey(userId)) {
            _profileImageCache[userId] = userData['profileImageUrl'].obs;
          } else {
            _profileImageCache[userId]!.value = userData['profileImageUrl'];
          }
        }

        debugPrint('UserCacheController: Cached user data for $userId');
        return userData;
      }
    } catch (e) {
      debugPrint(
        'UserCacheController: Error fetching user data for $userId: $e',
      );
    }

    return null;
  }

  /// Get profile image URL with caching
  Future<String?> getProfileImageUrl(String userId) async {
    // Return cached image URL if available
    if (_profileImageCache.containsKey(userId)) {
      return _profileImageCache[userId]!.value;
    }

    // Get from user data
    final userData = await getUserData(userId);
    final imageUrl = userData?['profileImageUrl'];

    if (imageUrl != null) {
      if (!_profileImageCache.containsKey(userId)) {
        _profileImageCache[userId] = imageUrl.obs;
      } else {
        _profileImageCache[userId]!.value = imageUrl;
      }
    }

    return imageUrl;
  }

  /// Get display name with caching
  Future<String> getDisplayName(String userId) async {
    final userData = await getUserData(userId);
    return userData?['name'] ?? userData?['username'] ?? 'Unknown User';
  }

  /// Update user data in cache and notify all listeners
  void updateUserData(String userId, Map<String, dynamic> newData) {
    debugPrint(
      'UserCacheController: Updating user data for $userId with keys: ${newData.keys}',
    );

    // Create a new map to trigger reactive update
    final currentData = Map<String, dynamic>.from(_userCache[userId] ?? {});
    currentData.addAll(newData);

    // Update user cache - this will automatically trigger reactive updates
    _userCache[userId] = currentData;

    // Update profile image cache if changed - this will automatically trigger reactive updates
    if (newData.containsKey('profileImageUrl') &&
        newData['profileImageUrl'] != null) {
      final imageUrl = newData['profileImageUrl'] as String;
      if (!_profileImageCache.containsKey(userId)) {
        _profileImageCache[userId] = imageUrl.obs;
      } else {
        _profileImageCache[userId]!.value = imageUrl;
      }
    }

    debugPrint('UserCacheController: User data updated for $userId');
  }

  /// Update profile image in cache and notify all listeners
  void updateProfileImage(String userId, String newImageUrl) {
    debugPrint(
      'UserCacheController: Updating profile image for $userId to $newImageUrl',
    );

    debugPrint(
      'UserCacheController: Cache before update: ${_profileImageCache.toString()}',
    );

    // Update profile image cache - this will automatically trigger reactive updates
    if (!_profileImageCache.containsKey(userId)) {
      _profileImageCache[userId] = newImageUrl.obs;
    } else {
      _profileImageCache[userId]!.value = newImageUrl;
    }

    // Force reactive update by incrementing trigger
    _profileImageUpdateTrigger.value++;

    debugPrint(
      'UserCacheController: Cache after update: ${_profileImageCache.toString()}',
    );

    // Update user cache if exists
    if (_userCache.containsKey(userId)) {
      final currentData = Map<String, dynamic>.from(_userCache[userId]!);
      currentData['profileImageUrl'] = newImageUrl;
      _userCache[userId] = currentData;
      debugPrint('UserCacheController: User cache also updated');
    } else {
      debugPrint('UserCacheController: User cache does not exist for $userId');
    }

    debugPrint(
      'UserCacheController: Profile image updated for $userId, trigger: ${_profileImageUpdateTrigger.value}',
    );
  }

  /// Clear cache for a specific user
  void clearUserCache(String userId) {
    _userCache.remove(userId);
    _profileImageCache.remove(userId);
    debugPrint('UserCacheController: Cleared cache for user $userId');
  }

  /// Clear all cached data
  void clearAllCache() {
    _userCache.clear();
    _profileImageCache.clear();
    debugPrint('UserCacheController: Cleared all cache');
  }

  /// Get multiple users data efficiently
  Future<Map<String, Map<String, dynamic>>> getMultipleUsersData(
    List<String> userIds,
  ) async {
    final result = <String, Map<String, dynamic>>{};
    final uncachedIds = <String>[];

    // Check cache first
    for (final userId in userIds) {
      if (_userCache.containsKey(userId)) {
        result[userId] = _userCache[userId]!;
      } else {
        uncachedIds.add(userId);
      }
    }

    // Fetch uncached users
    if (uncachedIds.isNotEmpty) {
      try {
        final docs =
            await _firestore
                .collection('users')
                .where(FieldPath.documentId, whereIn: uncachedIds)
                .get();

        for (final doc in docs.docs) {
          if (doc.exists) {
            final userData = doc.data();
            userData['id'] = doc.id;

            // Cache and add to result
            _userCache[doc.id] = userData;
            result[doc.id] = userData;

            // Cache profile image
            if (userData['profileImageUrl'] != null) {
              final imageUrl = userData['profileImageUrl'] as String;
              if (!_profileImageCache.containsKey(doc.id)) {
                _profileImageCache[doc.id] = imageUrl.obs;
              } else {
                _profileImageCache[doc.id]!.value = imageUrl;
              }
            }
          }
        }
      } catch (e) {
        debugPrint(
          'UserCacheController: Error fetching multiple users data: $e',
        );
      }
    }

    return result;
  }

  /// Listen to real-time updates for a user
  void listenToUserUpdates(String userId) {
    _firestore.collection('users').doc(userId).snapshots().listen((doc) {
      if (doc.exists) {
        final userData = doc.data()!;
        userData['id'] = doc.id;

        updateUserData(userId, userData);
        debugPrint('UserCacheController: Real-time update for user $userId');
      }
    });
  }

  /// Get reactive stream for a user's profile image
  Stream<String?> getProfileImageStream(String userId) {
    return getProfileImageRx(userId).stream;
  }

  /// Get reactive stream for a user's data
  Stream<Map<String, dynamic>?> getUserDataStream(String userId) {
    return _userCache.stream.map((cache) => cache[userId]);
  }
}
