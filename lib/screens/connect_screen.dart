import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/follow_service.dart';
import '../widgets/reactive_profile_image.dart';

class ConnectScreen extends StatelessWidget {
  ConnectScreen({super.key});

  final FollowService _followService = FollowService();

  Future<void> _toggleFollow(String targetUid) async {
    final isFollowing = await _followService.isFollowingStream(targetUid).first;

    if (isFollowing) {
      await _followService.unfollowUser(targetUid);
    } else {
      await _followService.followUser(targetUid);
    }
  }

  Stream<bool> _isFollowingStream(String targetUid) {
    return _followService.isFollowingStream(targetUid);
  }

  @override
  Widget build(BuildContext context) {
    final currentUserUid = FirebaseAuth.instance.currentUser?.uid;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Connect',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance.collection('users').snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return const Center(child: Text('Something went wrong'));
          }
          if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
            return const Center(child: Text('No users found'));
          }

          final users =
              snapshot.data!.docs
                  .where((doc) => doc.id != currentUserUid)
                  .toList();

          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: users.length,
            separatorBuilder: (_, __) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final userDoc = users[index];
              final user = userDoc.data() as Map<String, dynamic>;
              final username = user['username'] ?? 'N/A';

              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12.withOpacity(0.05),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    ReactiveProfileImage(
                      userId: userDoc.id,
                      radius: 24,
                      fallbackIcon: const Icon(
                        Icons.person,
                        size: 24,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        username,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    StreamBuilder<bool>(
                      stream: _isFollowingStream(userDoc.id),
                      builder: (context, followSnap) {
                        final isFollowing = followSnap.data ?? false;
                        return ElevatedButton(
                          onPressed: () => _toggleFollow(userDoc.id),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                isFollowing
                                    ? Colors.grey.shade300
                                    : const Color(0xFF5159FF),
                            foregroundColor:
                                isFollowing ? Colors.black : Colors.white,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(isFollowing ? 'Unfollow' : 'Follow'),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
